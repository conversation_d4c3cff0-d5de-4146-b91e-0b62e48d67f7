<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NotifyBee Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .vertical-menu-list__item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .food-background {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
        }
        .text-orange {
            color: #ff6b35;
        }
        .bg-orange-light {
            background-color: #fff5f0;
        }
        .container-shadowed-2 {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>NotifyBee Modal Test</h1>
        
        <ul class="arabas">
            <!-- Test Menu Item 1 -->
            <li class="vertical-menu-list__item bg-orange-light container-shadowed-2 aramaalan">
                <div class="row">
                    <div class="col-4 pe-1">
                        <div class="food-background">
                            <div style="background-image:url(https://via.placeholder.com/100x100/ff6b35/ffffff?text=Burger);height:90%;width:90%;background-size:cover;background-position:center;"></div>
                        </div>
                    </div>
                    <div class="col-8">
                        <h6 data-bs-toggle="modal" id="urunclick1" data-bs-target="#menuModal1">Test Burger</h6>
                        <p>Kısa açıklama burası...</p>
                        <span class="text-orange float-start mt-2 me-2">45.00TL</span>
                        <div class="float-end">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#menuModal1" class="btn btn-primary btn-sm">
                                <i class="fa-solid fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </li>
            
            <!-- Test Menu Item 2 -->
            <li class="vertical-menu-list__item bg-orange-light container-shadowed-2 aramaalan">
                <div class="row">
                    <div class="col-4 pe-1">
                        <div class="food-background">
                            <div style="background-image:url(https://via.placeholder.com/100x100/4ecdc4/ffffff?text=Pizza);height:90%;width:90%;background-size:cover;background-position:center;"></div>
                        </div>
                    </div>
                    <div class="col-8">
                        <h6 data-bs-toggle="modal" id="urunclick2" data-bs-target="#menuModal2">Test Pizza</h6>
                        <p>Pizza kısa açıklama...</p>
                        <span class="text-orange float-start mt-2 me-2">55.00TL</span>
                        <div class="float-end">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#menuModal2" class="btn btn-primary btn-sm">
                                <i class="fa-solid fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- Modal 1 -->
    <div class="modal modal-bottom fade" id="menuModal1" tabindex="-1" aria-labelledby="menuModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" data-bs-dismiss="modal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="2" viewBox="0 0 29 2">
                        <line id="Line_1" data-name="Line 1" x2="27" transform="translate(1 1)" fill="none" stroke="#1c1c1c" stroke-linecap="round" stroke-width="2"></line>
                    </svg>
                </div>
                <div class="modal-body pt-0">
                    <div class="card">
                        <h6 class="card-title text-center">Test Restaurant</h6>
                        <div class="card-header">
                            <img src="https://via.placeholder.com/300x200/ff6b35/ffffff?text=Burger" class="card-img-top">
                            <h6 class="mt-3 mb-2 text-white">Test Burger</h6>
                            <p class="text-white">
                                Bu tam açıklama metnidir. Özel burger köfte, katkısız klasik burger ekmeği, burger sos taban, jalapeno, köz kapya biber, çift dilim cheddar peyniri, çıtır soğan, acı sos, baharatlı patates cipsi ve özel sos ile servis edilir.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal 2 -->
    <div class="modal modal-bottom fade" id="menuModal2" tabindex="-1" aria-labelledby="menuModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" data-bs-dismiss="modal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="2" viewBox="0 0 29 2">
                        <line id="Line_1" data-name="Line 1" x2="27" transform="translate(1 1)" fill="none" stroke="#1c1c1c" stroke-linecap="round" stroke-width="2"></line>
                    </svg>
                </div>
                <div class="modal-body pt-0">
                    <div class="card">
                        <h6 class="card-title text-center">Test Restaurant</h6>
                        <div class="card-header">
                            <img src="https://via.placeholder.com/300x200/4ecdc4/ffffff?text=Pizza" class="card-img-top">
                            <h6 class="mt-3 mb-2 text-white">Test Pizza</h6>
                            <p class="text-white">
                                Bu da pizza için tam açıklama metnidir. İtalyan hamuru, domates sosu, mozzarella peyniri, taze fesleğen, zeytinyağı ve özel baharatlar ile hazırlanmış lezzetli pizza.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/crawler.js"></script>
    
    <script>
        // Test fonksiyonları için butonlar ekle
        document.addEventListener('DOMContentLoaded', function() {
            const testButtons = document.createElement('div');
            testButtons.className = 'container mt-4';
            testButtons.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Test Fonksiyonları</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info me-2" onclick="testModalDescriptions()">Modal Açıklamaları Test Et</button>
                        <button class="btn btn-warning me-2" onclick="testAllDescriptionSelectors()">Tüm Seçicileri Test Et</button>
                        <button class="btn btn-success me-2" onclick="analyzePageHTML()">Sayfa HTML Analiz Et</button>
                        <button class="btn btn-secondary me-2" onclick="console.clear()">Konsolu Temizle</button>
                        <button class="btn btn-primary" onclick="testCrawlerSelectors()">Crawler Seçicilerini Test Et</button>
                    </div>
                </div>
            `;
            document.body.appendChild(testButtons);

            // Otomatik test çalıştır
            setTimeout(() => {
                console.log('=== OTOMATİK TEST BAŞLADI ===');
                analyzePageHTML();
                testAllDescriptionSelectors();
            }, 1000);
        });

        // Crawler seçicilerini test et
        function testCrawlerSelectors() {
            console.log('=== CRAWLER SEÇİCİLERİ TESTİ ===');

            const selectors = {
                container: '.arabas',
                item: '.vertical-menu-list__item',
                name: 'h6',
                description_modal: '.modal .card-header p.text-white', // Modal açıklamaları
                description_short: '.col-8 p', // Kısa açıklamalar
                price: '.text-orange',
                image: '.food-background div[style*="background-image"]'
            };

            Object.keys(selectors).forEach(key => {
                const selector = selectors[key];
                try {
                    const elements = document.querySelectorAll(selector);
                    console.log(`${key} (${selector}): ${elements.length} element bulundu`);

                    if (elements.length > 0 && key.includes('description')) {
                        elements.forEach((el, index) => {
                            console.log(`  ${key} ${index + 1}:`, el.textContent.trim());
                        });
                    }
                } catch (error) {
                    console.error(`${key} seçici hatası:`, error);
                }
            });

            // Gerçek crawler test'i
            console.log('\n=== GERÇEKÇİ CRAWLER SİMÜLASYONU ===');
            const container = document.querySelector('.arabas');
            if (container) {
                const items = container.querySelectorAll('.vertical-menu-list__item');
                console.log(`${items.length} menu item bulundu`);

                items.forEach((item, index) => {
                    console.log(`\nItem ${index + 1}:`);

                    const name = item.querySelector('h6');
                    const shortDesc = item.querySelector('.col-8 p');
                    const price = item.querySelector('.text-orange');
                    const image = item.querySelector('.food-background div[style*="background-image"]');

                    console.log('  İsim:', name ? name.textContent.trim() : 'Yok');
                    console.log('  Kısa açıklama:', shortDesc ? shortDesc.textContent.trim() : 'Yok');
                    console.log('  Fiyat:', price ? price.textContent.trim() : 'Yok');
                    console.log('  Resim:', image ? 'Var' : 'Yok');
                });
            }
        }
    </script>
</body>
</html>
