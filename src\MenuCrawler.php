<?php

namespace ContentCrawler;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Symfony\Component\DomCrawler\Crawler;

class MenuCrawler
{
    private $client;
    private $userAgent;

    public function __construct()
    {
        $this->userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

        $this->client = new Client([
            'timeout' => 30,
            'verify' => false,
            'headers' => [
                'User-Agent' => $this->userAgent
            ]
        ]);
    }

    /**
     * Crawl menu data from a given URL using CSS selectors
     */
    public function crawlMenu($url, $selectors)
    {
        try {
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new \InvalidArgumentException('Geçersiz URL formatı');
            }

            // Fetch HTML content
            $response = $this->client->get($url);
            $html = $response->getBody()->getContents();

            if (empty($html)) {
                throw new \Exception('<PERSON>fa içeriği boş: ' . $url);
            }

            // Create DOM crawler
            $crawler = new Crawler($html);

            $menuItems = [];

            // Find menu container
            $containerSelector = $selectors['container'];
            if (empty($containerSelector)) {
                throw new \Exception('Menü konteyneri seçicisi boş');
            }

            $menuContainer = $crawler->filter($containerSelector);

            if ($menuContainer->count() === 0) {
                throw new \Exception('Menü konteyneri bulunamadı: ' . $containerSelector);
            }

            // Extract menu items
            $itemSelector = $selectors['item'];
            if (empty($itemSelector)) {
                throw new \Exception('Menü öğesi seçicisi boş');
            }

            $items = $menuContainer->filter($itemSelector);

            $items->each(function (Crawler $item) use (&$menuItems, $selectors, $url, $crawler) {
                $menuItem = [
                    'name' => $this->extractText($item, $selectors['name']),
                    'description' => $this->extractText($item, $selectors['description']),
                    'price' => $this->extractText($item, $selectors['price']),
                    'image' => $this->extractImage($item, $selectors['image'], $url)
                ];

                // NotifyBee özel durumu: Modal açıklamalarını çek
                if (strpos($url, 'notifybee') !== false && empty($menuItem['description'])) {
                    $modalDescription = $this->extractModalDescription($item, $crawler);
                    if (!empty($modalDescription)) {
                        $menuItem['description'] = $modalDescription;
                    }
                }

                // Debug: Log description extraction
                if (!empty($selectors['description'])) {
                    error_log("DEBUG - Description selector: " . $selectors['description']);
                    error_log("DEBUG - Found description: " . $menuItem['description']);

                    // Try to find all matching elements for debug
                    try {
                        $descElements = $item->filter($selectors['description']);
                        error_log("DEBUG - Description elements found: " . $descElements->count());
                        if ($descElements->count() > 0) {
                            error_log("DEBUG - First description HTML: " . $descElements->first()->html());
                        }
                    } catch (\Exception $e) {
                        error_log("DEBUG - Description extraction error: " . $e->getMessage());
                    }
                }

                // Only add if we have at least name or price
                if (!empty($menuItem['name']) || !empty($menuItem['price'])) {
                    $menuItems[] = $menuItem;
                }
            });

            return [
                'success' => true,
                'data' => $menuItems,
                'count' => count($menuItems),
                'url' => $url,
                'timestamp' => date('Y-m-d H:i:s')
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => 'HTTP Hatası: ' . $e->getMessage(),
                'url' => $url
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'url' => $url
            ];
        }
    }

    /**
     * Extract text content from element using selector
     */
    private function extractText(Crawler $element, $selector)
    {
        if (empty($selector)) return '';

        try {
            $found = $element->filter($selector);
            return $found->count() > 0 ? trim($found->text()) : '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Extract image URL from element using selector
     */
    private function extractImage(Crawler $element, $selector, $baseUrl)
    {
        if (empty($selector)) return '';

        try {
            $found = $element->filter($selector);
            if ($found->count() === 0) return '';

            // Try to get src attribute first
            $src = $found->attr('src') ?: $found->attr('data-src') ?: '';

            // If no src found, try to extract from style attribute (background-image)
            if (empty($src)) {
                $style = $found->attr('style');
                if (!empty($style)) {
                    // Extract URL from background-image: url(...)
                    if (preg_match('/background-image\s*:\s*url\s*\(\s*["\']?([^"\']+)["\']?\s*\)/i', $style, $matches)) {
                        $src = $matches[1];
                    }
                }
            }

            // If still no src, try to find child elements with background-image
            if (empty($src)) {
                $childWithBg = $found->filter('[style*="background-image"]');
                if ($childWithBg->count() > 0) {
                    $style = $childWithBg->attr('style');
                    if (preg_match('/background-image\s*:\s*url\s*\(\s*["\']?([^"\']+)["\']?\s*\)/i', $style, $matches)) {
                        $src = $matches[1];
                    }
                }
            }

            if (empty($src)) return '';

            // Return the URL as-is (don't convert relative to absolute)
            return $src;
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Extract modal description for NotifyBee-style sites
     */
    private function extractModalDescription(Crawler $item, Crawler $fullCrawler)
    {
        try {
            // Modal butonunu bul
            $modalButton = $item->filter('[data-bs-target^="#menuModal"]');
            if ($modalButton->count() === 0) {
                return '';
            }

            // Modal ID'sini çıkar
            $modalTarget = $modalButton->attr('data-bs-target');
            if (empty($modalTarget)) {
                return '';
            }

            // Modal'ı bul
            $modal = $fullCrawler->filter($modalTarget);
            if ($modal->count() === 0) {
                return '';
            }

            // Modal içindeki açıklamayı bul
            $description = $modal->filter('.card-header p.text-white');
            if ($description->count() > 0) {
                return trim($description->text());
            }

            // Alternatif seçiciler dene
            $altDescription = $modal->filter('.modal-body .card-header p');
            if ($altDescription->count() > 0) {
                return trim($altDescription->text());
            }

            return '';
        } catch (\Exception $e) {
            error_log("Modal description extraction error: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Discover categories from NotifyBee menu page
     */
    public function discoverNotifyBeeCategories($menuUrl)
    {
        try {
            // Validate URL
            if (!filter_var($menuUrl, FILTER_VALIDATE_URL)) {
                throw new \InvalidArgumentException('Geçersiz URL formatı');
            }

            // Fetch HTML content
            $response = $this->client->get($menuUrl);
            $html = $response->getBody()->getContents();

            if (empty($html)) {
                throw new \Exception('Sayfa içeriği boş: ' . $menuUrl);
            }

            // Create DOM crawler
            $crawler = new Crawler($html);

            $categories = [];

            // NotifyBee kategori linklerini bul
            // Kategoriler genellikle li > a[href*="menudetay"] formatında
            $categoryLinks = $crawler->filter('li a[href*="menudetay"]');

            $categoryLinks->each(function (Crawler $link) use (&$categories) {
                $href = $link->attr('href');
                $categoryName = trim($link->text());

                // Boş kategori adlarını atla
                if (empty($categoryName)) {
                    return;
                }

                // Tam URL oluştur
                if (strpos($href, 'http') !== 0) {
                    $href = 'https://notifybee.com.tr' . $href;
                }

                $categories[] = [
                    'name' => $categoryName,
                    'url' => $href
                ];
            });

            return [
                'success' => true,
                'categories' => $categories,
                'count' => count($categories),
                'source_url' => $menuUrl
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'source_url' => $menuUrl
            ];
        }
    }

    /**
     * Check if a category URL has subcategories
     */
    private function hasSubcategories($categoryUrl)
    {
        try {
            $response = $this->client->get($categoryUrl);
            $html = $response->getBody()->getContents();
            $crawler = new Crawler($html);

            // Alt kategoriler varsa, genellikle yine li > a[href*="menudetay"] formatında olur
            $subcategoryLinks = $crawler->filter('li a[href*="menudetay"]');

            // Eğer 1'den fazla link varsa alt kategoriler var demektir
            return $subcategoryLinks->count() > 1;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Discover subcategories from a category URL
     */
    private function discoverSubcategories($categoryUrl)
    {
        try {
            $response = $this->client->get($categoryUrl);
            $html = $response->getBody()->getContents();
            $crawler = new Crawler($html);

            $subcategories = [];
            $subcategoryLinks = $crawler->filter('li a[href*="menudetay"]');

            $subcategoryLinks->each(function (Crawler $link) use (&$subcategories) {
                $href = $link->attr('href');
                $categoryName = trim($link->text());

                if (empty($categoryName)) {
                    return;
                }

                // Tam URL oluştur
                if (strpos($href, 'http') !== 0) {
                    $href = 'https://notifybee.com.tr' . $href;
                }

                $subcategories[] = [
                    'name' => $categoryName,
                    'url' => $href
                ];
            });

            return $subcategories;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Auto-discover and crawl all categories from a NotifyBee menu URL
     */
    public function crawlNotifyBeeMenuWithCategories($menuUrl, $selectors)
    {
        try {
            // First discover categories
            $categoryResult = $this->discoverNotifyBeeCategories($menuUrl);

            if (!$categoryResult['success']) {
                return $categoryResult;
            }

            $allMenuItems = [];
            $crawlResults = [];

            // Crawl each category
            foreach ($categoryResult['categories'] as $category) {
                $categoryName = $category['name'];
                $categoryUrl = $category['url'];

                // Alt kategoriler var mı kontrol et
                if ($this->hasSubcategories($categoryUrl)) {
                    // Alt kategorileri keşfet
                    $subcategories = $this->discoverSubcategories($categoryUrl);

                    if (!empty($subcategories)) {
                        // Her alt kategoriyi crawl et
                        foreach ($subcategories as $subcategory) {
                            $subcategoryName = $categoryName . ' > ' . $subcategory['name'];
                            $subcategoryUrl = $subcategory['url'];

                            $crawlResult = $this->crawlMenu($subcategoryUrl, $selectors);

                            if ($crawlResult['success']) {
                                $itemsWithCategory = array_map(function($item) use ($subcategoryName, $subcategoryUrl) {
                                    $item['category'] = $subcategoryName;
                                    $item['source_url'] = $subcategoryUrl;
                                    return $item;
                                }, $crawlResult['data']);

                                $allMenuItems = array_merge($allMenuItems, $itemsWithCategory);

                                $crawlResults[] = [
                                    'category' => $subcategoryName,
                                    'url' => $subcategoryUrl,
                                    'count' => $crawlResult['count'],
                                    'success' => true,
                                    'is_subcategory' => true
                                ];
                            } else {
                                $crawlResults[] = [
                                    'category' => $subcategoryName,
                                    'url' => $subcategoryUrl,
                                    'error' => $crawlResult['error'],
                                    'success' => false,
                                    'is_subcategory' => true
                                ];
                            }

                            usleep(500000); // 0.5 seconds
                        }
                    } else {
                        // Alt kategori keşfi başarısız, ana kategoriyi crawl et
                        $crawlResult = $this->crawlMenu($categoryUrl, $selectors);

                        if ($crawlResult['success']) {
                            $itemsWithCategory = array_map(function($item) use ($categoryName, $categoryUrl) {
                                $item['category'] = $categoryName;
                                $item['source_url'] = $categoryUrl;
                                return $item;
                            }, $crawlResult['data']);

                            $allMenuItems = array_merge($allMenuItems, $itemsWithCategory);

                            $crawlResults[] = [
                                'category' => $categoryName,
                                'url' => $categoryUrl,
                                'count' => $crawlResult['count'],
                                'success' => true
                            ];
                        } else {
                            $crawlResults[] = [
                                'category' => $categoryName,
                                'url' => $categoryUrl,
                                'error' => $crawlResult['error'],
                                'success' => false
                            ];
                        }
                    }
                } else {
                    // Alt kategori yok, direkt crawl et
                    $crawlResult = $this->crawlMenu($categoryUrl, $selectors);

                    if ($crawlResult['success']) {
                        $itemsWithCategory = array_map(function($item) use ($categoryName, $categoryUrl) {
                            $item['category'] = $categoryName;
                            $item['source_url'] = $categoryUrl;
                            return $item;
                        }, $crawlResult['data']);

                        $allMenuItems = array_merge($allMenuItems, $itemsWithCategory);

                        $crawlResults[] = [
                            'category' => $categoryName,
                            'url' => $categoryUrl,
                            'count' => $crawlResult['count'],
                            'success' => true
                        ];
                    } else {
                        $crawlResults[] = [
                            'category' => $categoryName,
                            'url' => $categoryUrl,
                            'error' => $crawlResult['error'],
                            'success' => false
                        ];
                    }
                }

                // Small delay between categories
                usleep(500000); // 0.5 seconds
            }

            return [
                'success' => true,
                'data' => $allMenuItems,
                'count' => count($allMenuItems),
                'categories_discovered' => count($categoryResult['categories']),
                'crawl_results' => $crawlResults,
                'source_menu_url' => $menuUrl,
                'timestamp' => date('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'source_menu_url' => $menuUrl
            ];
        }
    }

    /**
     * Test selectors on a URL to see what data can be extracted
     */
    public function testSelectors($url, $selectors)
    {
        try {
            // Fetch HTML content
            $response = $this->client->get($url);
            $html = $response->getBody()->getContents();

            if (empty($html)) {
                return ['success' => false, 'error' => 'Sayfa içeriği boş'];
            }

            // Create DOM crawler
            $crawler = new Crawler($html);

            $results = [];

            foreach ($selectors as $key => $selector) {
                if (empty($selector)) continue;

                try {
                    $elements = $crawler->filter($selector);
                    $results[$key] = [
                        'selector' => $selector,
                        'found_count' => $elements->count(),
                        'sample_data' => []
                    ];

                    // Get first 3 samples
                    $sampleCount = min(3, $elements->count());
                    for ($i = 0; $i < $sampleCount; $i++) {
                        $element = $elements->eq($i);
                        $results[$key]['sample_data'][] = [
                            'text' => trim($element->text()),
                            'html' => $element->html()
                        ];
                    }
                } catch (\Exception $e) {
                    $results[$key] = [
                        'selector' => $selector,
                        'found_count' => 0,
                        'error' => $e->getMessage(),
                        'sample_data' => []
                    ];
                }
            }

            return ['success' => true, 'results' => $results];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
