{"name": "symfony/dom-crawler", "type": "library", "description": "Eases DOM navigation for HTML and XML documents", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "masterminds/html5": "^2.6"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0"}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}